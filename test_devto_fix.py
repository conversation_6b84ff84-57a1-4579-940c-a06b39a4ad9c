#!/usr/bin/env python3
"""
Test script to verify Dev.to canonical URL fix
"""

import requests
import json

# Test data - same as your example but with different approaches
test_data_original = {
    "title": "Building a React Todo App: Complete Guide",
    "body_markdown": "# Building a Todo App with <PERSON>act\n\nIn this comprehensive tutorial, we'll build a fully functional todo application using React.\n\n## Prerequisites\n\n- Basic knowledge of JavaScript\n- Understanding of React concepts\n- Node.js installed\n\n## Project Setup\n\n```bash\nnpx create-react-app todo-app\ncd todo-app\nnpm start\n```\n\n## Components Structure\n\n### App Component\n```jsx\nfunction App() {\n  const [todos, setTodos] = useState([]);\n  \n  return (\n    <div className=\"App\">\n      <TodoList todos={todos} />\n    </div>\n  );\n}\n```\n\n### TodoItem Component\n```jsx\nfunction TodoItem({ todo, onToggle, onDelete }) {\n  return (\n    <div className=\"todo-item\">\n      <span>{todo.text}</span>\n      <button onClick={() => onToggle(todo.id)}>Toggle</button>\n      <button onClick={() => onDelete(todo.id)}>Delete</button>\n    </div>\n  );\n}\n```\n\n## State Management\n\nWe'll use React hooks for state management:\n\n```jsx\nconst [todos, setTodos] = useState([]);\nconst [inputValue, setInputValue] = useState('');\n```\n\n## Adding Todos\n\n```jsx\nconst addTodo = () => {\n  if (inputValue.trim()) {\n    setTodos([...todos, {\n      id: Date.now(),\n      text: inputValue,\n      completed: false\n    }]);\n    setInputValue('');\n  }\n};\n```\n\n## Styling\n\nAdd some basic CSS for a clean look:\n\n```css\n.todo-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 10px;\n  border: 1px solid #ddd;\n  margin: 5px 0;\n}\n```\n\n## Conclusion\n\nYou now have a fully functional todo app! This project covers:\n\n- Component composition\n- State management with hooks\n- Event handling\n- Conditional rendering\n\nNext steps could include:\n- Adding local storage\n- Implementing drag and drop\n- Adding due dates\n- Creating categories\n\nHappy coding! 🚀",
    "published": True,
    "tags": ["react", "javascript", "tutorial", "beginners"],
    "canonical_url": "https://yourblog.com/react-todo-guide",
    "description": "Step-by-step guide to building a complete todo application with React, covering components, state management, and styling.",
    "main_image": "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=1000",
    "series": "React Beginner Projects"
}

# Test data without canonical URL
test_data_no_canonical = test_data_original.copy()
del test_data_no_canonical['canonical_url']

# Test data with auto_fix_canonical enabled
test_data_auto_fix = test_data_original.copy()
test_data_auto_fix['auto_fix_canonical'] = True

def test_devto_upload(data, test_name):
    """Test Dev.to upload with different configurations"""
    print(f"\n=== Testing {test_name} ===")
    
    # Replace with your actual API endpoint
    url = "http://localhost:8000/api/devto-blog-upload/"
    
    headers = {
        'Authorization': 'Bearer YOUR_JWT_TOKEN',  # Replace with actual token
        'brand': 'YOUR_BRAND_ID',  # Replace with actual brand ID
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 201:
            print("✅ Success!")
        elif response.status_code == 400:
            response_data = response.json()
            if response_data.get('error_type') == 'canonical_url_duplicate':
                print("⚠️  Canonical URL conflict detected")
                print("Suggestions provided:")
                for suggestion in response_data.get('suggestions', []):
                    print(f"  - {suggestion}")
            else:
                print("❌ Other error occurred")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error making request: {e}")

if __name__ == "__main__":
    print("Dev.to Upload Test Script")
    print("=" * 50)
    
    print("\nThis script demonstrates the different ways to handle canonical URL conflicts:")
    print("1. Original request (will fail with duplicate canonical URL)")
    print("2. Request without canonical URL (should work)")
    print("3. Request with auto_fix_canonical=True (should work with modified URL)")
    
    # Uncomment the tests you want to run:
    # test_devto_upload(test_data_original, "Original with duplicate canonical URL")
    # test_devto_upload(test_data_no_canonical, "Without canonical URL")
    # test_devto_upload(test_data_auto_fix, "With auto_fix_canonical enabled")
    
    print("\n" + "=" * 50)
    print("To run these tests:")
    print("1. Update the URL, headers with your actual values")
    print("2. Uncomment the test calls at the bottom")
    print("3. Run: python test_devto_fix.py")
