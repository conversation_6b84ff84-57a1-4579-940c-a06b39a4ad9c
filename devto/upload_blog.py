import requests
import json
from datetime import datetime
import uuid

# Dev.to API Configuration
DEVTO_API_BASE_URL = 'https://dev.to/api'

def create_devto_article(api_key, title, body_markdown, published=True, tags=None, 
                        canonical_url=None, description=None, main_image=None, series=None):
    """
    Create a new article on Dev.to
    
    Args:
        api_key (str): Dev.to API key
        title (str): Article title
        body_markdown (str): Article content in markdown format
        published (bool): Whether to publish immediately (default: True)
        tags (list): List of tags for the article
        canonical_url (str): Canonical URL if cross-posting
        description (str): Article description
        main_image (str): URL of the main image
        series (str): Series name if part of a series
        
    Returns:
        tuple: (success, article_data/error_message)
    """
    try:
        headers = {
            'api-key': api_key,
            'Content-Type': 'application/json'
        }
        
        # Prepare article data
        article_data = {
            'title': title,
            'body_markdown': body_markdown,
            'published': published
        }
        
        # Add optional fields if provided
        if tags and isinstance(tags, list):
            article_data['tags'] = tags
            
        if canonical_url:
            article_data['canonical_url'] = canonical_url
            
        if description:
            article_data['description'] = description
            
        if main_image:
            article_data['main_image'] = main_image
            
        if series:
            article_data['series'] = series
        
        payload = {'article': article_data}
        
        response = requests.post(
            f'{DEVTO_API_BASE_URL}/articles',
            headers=headers,
            json=payload
        )
        
        if response.status_code == 201:
            article_response = response.json()
            return True, article_response
        else:
            # Parse error response for better error handling
            try:
                error_response = response.json()
                if response.status_code == 422 and 'error' in error_response:
                    error_msg = error_response['error']
                else:
                    error_msg = f"Error creating article: {response.status_code} - {response.text}"
            except:
                error_msg = f"Error creating article: {response.status_code} - {response.text}"

            print(error_msg)
            return False, error_msg
            
    except Exception as e:
        error_msg = f"Exception creating Dev.to article: {e}"
        print(error_msg)
        return False, error_msg

def update_devto_article(api_key, article_id, title=None, body_markdown=None, 
                        published=None, tags=None, canonical_url=None, 
                        description=None, main_image=None, series=None):
    """
    Update an existing article on Dev.to
    
    Args:
        api_key (str): Dev.to API key
        article_id (int): ID of the article to update
        title (str): New article title
        body_markdown (str): New article content in markdown format
        published (bool): Whether to publish/unpublish
        tags (list): New list of tags for the article
        canonical_url (str): New canonical URL
        description (str): New article description
        main_image (str): New URL of the main image
        series (str): New series name
        
    Returns:
        tuple: (success, article_data/error_message)
    """
    try:
        headers = {
            'api-key': api_key,
            'Content-Type': 'application/json'
        }
        
        # Prepare article data with only provided fields
        article_data = {}
        
        if title is not None:
            article_data['title'] = title
            
        if body_markdown is not None:
            article_data['body_markdown'] = body_markdown
            
        if published is not None:
            article_data['published'] = published
            
        if tags is not None and isinstance(tags, list):
            article_data['tags'] = tags
            
        if canonical_url is not None:
            article_data['canonical_url'] = canonical_url
            
        if description is not None:
            article_data['description'] = description
            
        if main_image is not None:
            article_data['main_image'] = main_image
            
        if series is not None:
            article_data['series'] = series
        
        payload = {'article': article_data}
        
        response = requests.put(
            f'{DEVTO_API_BASE_URL}/articles/{article_id}',
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            article_response = response.json()
            return True, article_response
        else:
            error_msg = f"Error updating article: {response.status_code} - {response.text}"
            print(error_msg)
            return False, error_msg
            
    except Exception as e:
        error_msg = f"Exception updating Dev.to article: {e}"
        print(error_msg)
        return False, error_msg

def get_devto_article(api_key, article_id):
    """
    Get a specific article from Dev.to
    
    Args:
        api_key (str): Dev.to API key
        article_id (int): ID of the article
        
    Returns:
        tuple: (success, article_data/error_message)
    """
    try:
        headers = {
            'api-key': api_key,
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f'{DEVTO_API_BASE_URL}/articles/{article_id}',
            headers=headers
        )
        
        if response.status_code == 200:
            article_response = response.json()
            return True, article_response
        else:
            error_msg = f"Error getting article: {response.status_code} - {response.text}"
            print(error_msg)
            return False, error_msg
            
    except Exception as e:
        error_msg = f"Exception getting Dev.to article: {e}"
        print(error_msg)
        return False, error_msg

def convert_html_to_markdown(html_content):
    """
    Convert HTML content to Markdown format for Dev.to
    This is a basic converter - you might want to use a library like html2text for better conversion
    
    Args:
        html_content (str): HTML content
        
    Returns:
        str: Markdown content
    """
    try:
        # Basic HTML to Markdown conversion
        # You can enhance this with html2text library for better conversion
        markdown_content = html_content
        
        # Basic replacements
        replacements = [
            ('<h1>', '# '),
            ('</h1>', '\n\n'),
            ('<h2>', '## '),
            ('</h2>', '\n\n'),
            ('<h3>', '### '),
            ('</h3>', '\n\n'),
            ('<p>', ''),
            ('</p>', '\n\n'),
            ('<br>', '\n'),
            ('<br/>', '\n'),
            ('<br />', '\n'),
            ('<strong>', '**'),
            ('</strong>', '**'),
            ('<b>', '**'),
            ('</b>', '**'),
            ('<em>', '*'),
            ('</em>', '*'),
            ('<i>', '*'),
            ('</i>', '*'),
        ]
        
        for old, new in replacements:
            markdown_content = markdown_content.replace(old, new)
            
        return markdown_content.strip()
        
    except Exception as e:
        print(f"Error converting HTML to Markdown: {e}")
        return html_content

def format_devto_tags(tags_string):
    """
    Format tags for Dev.to API

    Args:
        tags_string (str): Comma-separated tags string

    Returns:
        list: List of formatted tags
    """
    try:
        if not tags_string:
            return []

        # Split by comma and clean up
        tags = [tag.strip().lower() for tag in tags_string.split(',') if tag.strip()]

        # Dev.to has a limit of 4 tags per article
        return tags[:4]

    except Exception as e:
        print(f"Error formatting tags: {e}")
        return []

def make_canonical_url_unique(canonical_url):
    """
    Make canonical URL unique by appending a UUID if needed

    Args:
        canonical_url (str): Original canonical URL

    Returns:
        str: Unique canonical URL
    """
    try:
        if not canonical_url:
            return None

        # Add a unique identifier to make it unique
        unique_id = str(uuid.uuid4())[:8]

        # Check if URL already has query parameters
        if '?' in canonical_url:
            return f"{canonical_url}&ref={unique_id}"
        else:
            return f"{canonical_url}?ref={unique_id}"

    except Exception as e:
        print(f"Error making canonical URL unique: {e}")
        return canonical_url
