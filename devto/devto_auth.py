import requests
from django.conf import settings

# Dev.to API Configuration
DEVTO_API_BASE_URL = 'https://dev.to/api'

def validate_devto_api_key(api_key):
    """
    Validate Dev.to API key by making a request to get authenticated user details
    
    Args:
        api_key (str): Dev.to API key
        
    Returns:
        tuple: (is_valid, user_data)
    """
    try:
        headers = {
            'api-key': api_key,
            'Content-Type': 'application/json'
        }
        
        response = requests.get(f'{DEVTO_API_BASE_URL}/users/me', headers=headers)
        
        if response.status_code == 200:
            user_data = response.json()
            return True, user_data
        else:
            return False, None
            
    except Exception as e:
        print(f"Error validating Dev.to API key: {e}")
        return False, None

def get_devto_user_info(api_key):
    """
    Get authenticated user information from Dev.to
    
    Args:
        api_key (str): Dev.to API key
        
    Returns:
        dict: User information or None if error
    """
    try:
        headers = {
            'api-key': api_key,
            'Content-Type': 'application/json'
        }
        
        response = requests.get(f'{DEVTO_API_BASE_URL}/users/me', headers=headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error getting user info: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"Error getting Dev.to user info: {e}")
        return None

def get_devto_user_articles(api_key, page=1, per_page=30):
    """
    Get user's articles from Dev.to
    
    Args:
        api_key (str): Dev.to API key
        page (int): Page number for pagination
        per_page (int): Number of articles per page
        
    Returns:
        list: List of articles or None if error
    """
    try:
        headers = {
            'api-key': api_key,
            'Content-Type': 'application/json'
        }
        
        params = {
            'page': page,
            'per_page': per_page
        }
        
        response = requests.get(f'{DEVTO_API_BASE_URL}/articles/me', headers=headers, params=params)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error getting articles: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"Error getting Dev.to articles: {e}")
        return None
